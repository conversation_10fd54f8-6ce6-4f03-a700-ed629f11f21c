import warnings
import os
import cv2
from pathlib import Path
import torch
import torch.nn as nn
import json
import time
import numpy as np
from tqdm import tqdm
from PIL import Image, ImageDraw, ImageFont

warnings.filterwarnings('ignore')
from ultralytics import YOLO

# 导入OCR相关库
try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False
    print("EasyOCR not available. Installing...")
    os.system("pip install easyocr")
    import easyocr
    EASYOCR_AVAILABLE = True

try:
    from cnocr import CnOcr
    CNOCR_AVAILABLE = True
except ImportError:
    CNOCR_AVAILABLE = False
    print("CnOCR not available. Installing...")
    os.system("pip install cnocr")
    from cnocr import CnOcr
    CNOCR_AVAILABLE = True

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    print("Tesseract not available. Installing...")
    os.system("pip install pytesseract")
    import pytesseract
    TESSERACT_AVAILABLE = True

# 移除MMOCR依赖，使用更简单的OCR引擎组合
MMOCR_AVAILABLE = False





class MultiTaskModel(nn.Module):
    """
    简化的多任务模型：YOLO目标检测 + CnOCR文字识别
    架构：YOLO目标检测 + CnOCR文字检测识别
    """
    def __init__(self, yolo_model_path: str, num_classes: int = 47):
        super(MultiTaskModel, self).__init__()

        # 加载预训练YOLO模型
        self.yolo_model = YOLO(yolo_model_path)
        self.num_classes = num_classes

        # 初始化OCR引擎
        self.init_ocr_engines()

        # 置信度阈值
        self.detection_conf_threshold = 0.15
        self.ocr_confidence_threshold = 0.05



    def init_ocr_engines(self):
        """初始化多个OCR引擎以提高识别精度"""
        self.ocr_engines = {}

        # EasyOCR引擎
        if EASYOCR_AVAILABLE:
            try:
                self.ocr_engines['easyocr'] = easyocr.Reader(['ch_sim', 'en'], gpu=torch.cuda.is_available())
                print("✓ EasyOCR引擎初始化成功")
            except Exception as e:
                print(f"✗ EasyOCR引擎初始化失败: {e}")

        # CnOCR引擎
        if CNOCR_AVAILABLE:
            try:
                self.ocr_engines['cnocr'] = CnOcr(
                    rec_model_name='densenet_lite_136-gru',
                    det_model_name='db_resnet18',
                    use_gpu=torch.cuda.is_available()
                )
                print("✓ CnOCR引擎初始化成功")
            except Exception as e:
                print(f"✗ CnOCR引擎初始化失败: {e}")

        # Tesseract引擎
        if TESSERACT_AVAILABLE:
            try:
                # 测试Tesseract是否可用
                pytesseract.get_tesseract_version()
                self.ocr_engines['tesseract'] = pytesseract
                print("✓ Tesseract引擎初始化成功")
            except Exception as e:
                print(f"✗ Tesseract引擎初始化失败: {e}")

   

        print(f"📊 已初始化 {len(self.ocr_engines)} 个OCR引擎: {list(self.ocr_engines.keys())}")

    def enhance_image_for_detection(self, image):
        """增强图像以提高检测精度"""
        try:
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            enhanced = cv2.GaussianBlur(enhanced, (3, 3), 0)

            # 锐化滤波器
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            enhanced = cv2.filter2D(enhanced, -1, kernel)

            # 转换回BGR格式
            if len(image.shape) == 3:
                enhanced = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)

            return enhanced
        except Exception as e:
            print(f"   ⚠️ 图像增强失败: {e}")
            return image

    def detect_objects_multiscale(self, image_path, conf_threshold=None):
        """多尺度YOLO目标检测以提高检测精度"""
        if conf_threshold is None:
            conf_threshold = self.detection_conf_threshold
        print(f"🔍 开始多尺度YOLO检测，置信度阈值: {conf_threshold}")
        all_detections = []
        scales = [0.8, 1.0, 1.2, 1.5]
        for scale in scales:
            print(f"   检测尺度: {scale}")
            scale_conf = conf_threshold * (0.9 if scale != 1.0 else 1.0)

            results = self.yolo_model.predict(
                image_path,
                conf=scale_conf,
                imgsz=int(640 * scale),  # 调整输入图像尺寸
                device='0' if torch.cuda.is_available() else 'cpu',
                verbose=False
            )

            # 收集检测结果
            for result in results:
                if hasattr(result, 'boxes') and result.boxes is not None:
                    boxes = result.boxes.xyxy.cpu().numpy()
                    confidences = result.boxes.conf.cpu().numpy()
                    class_ids = result.boxes.cls.cpu().numpy()

                    for box, conf, cls_id in zip(boxes, confidences, class_ids):
                        all_detections.append({
                            'bbox': box.tolist(),
                            'confidence': float(conf),
                            'class_id': int(cls_id),
                            'scale': scale
                        })

        print(f"🔍 多尺度检测完成，共检测到 {len(all_detections)} 个候选目标")

        # 使用NMS去除重复检测
        final_detections = self.apply_nms_to_detections(all_detections)
        print(f"✅ NMS后保留 {len(final_detections)} 个目标")

        return final_detections

    def apply_nms_to_detections(self, detections, iou_threshold=0.5):
        """
        对检测结果应用非极大值抑制
        """
        if not detections:
            return []

        import numpy as np

        # 按置信度排序
        detections = sorted(detections, key=lambda x: x['confidence'], reverse=True)

        # 转换为numpy数组便于计算
        boxes = np.array([d['bbox'] for d in detections])
        confidences = np.array([d['confidence'] for d in detections])

        # 计算面积
        areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])

        keep = []
        indices = np.arange(len(detections))

        while len(indices) > 0:
            # 选择置信度最高的
            current = indices[0]
            keep.append(current)

            if len(indices) == 1:
                break

            # 计算IoU
            current_box = boxes[current]
            other_boxes = boxes[indices[1:]]

            # 计算交集
            x1 = np.maximum(current_box[0], other_boxes[:, 0])
            y1 = np.maximum(current_box[1], other_boxes[:, 1])
            x2 = np.minimum(current_box[2], other_boxes[:, 2])
            y2 = np.minimum(current_box[3], other_boxes[:, 3])

            intersection = np.maximum(0, x2 - x1) * np.maximum(0, y2 - y1)
            union = areas[current] + areas[indices[1:]] - intersection

            iou = intersection / union

            # 保留IoU小于阈值的检测
            indices = indices[1:][iou < iou_threshold]

        return [detections[i] for i in keep]

    def detect_objects(self, image_path, conf_threshold=None):
        """
        使用YOLO进行目标检测（包含多尺度检测）
        """
        return self.detect_objects_multiscale(image_path, conf_threshold)



    def extract_text_regions(self, image, detection_results):
        """
        从检测结果中提取可能包含文字的区域
        """
        text_regions = []

        # 安全地检查检测结果
        if len(detection_results) > 0:
            result = detection_results[0]

            # 检查结果是否有boxes属性且不为空
            if hasattr(result, 'boxes') and result.boxes is not None and len(result.boxes) > 0:
                boxes = result.boxes

                for box in boxes:
                    # 获取边界框坐标
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0])
                    class_id = int(box.cls[0])

                    # 扩展边界框以包含可能的文字区域
                    h, w = image.shape[:2]
                    margin = 10
                    x1 = max(0, int(x1) - margin)
                    y1 = max(0, int(y1) - margin)
                    x2 = min(w, int(x2) + margin)
                    y2 = min(h, int(y2) + margin)

                    # 提取区域
                    region = image[y1:y2, x1:x2]

                    text_regions.append({
                        'region': region,
                        'bbox': (x1, y1, x2, y2),
                        'confidence': confidence,
                        'class_id': class_id
                    })

        return text_regions

    def recognize_text_easyocr(self, image_region):
        """
        使用EasyOCR识别文字
        """
        if 'easyocr' not in self.ocr_engines:
            return []

        try:
            results = self.ocr_engines['easyocr'].readtext(image_region)

            text_results = []
            for (bbox, text, confidence) in results:
                if confidence > self.ocr_confidence_threshold:
                    text_results.append({
                        'text': text,
                        'confidence': confidence,
                        'bbox': bbox,
                        'engine': 'easyocr'
                    })

            return text_results
        except Exception as e:
            print(f"EasyOCR识别错误: {e}")
            return []

    def sliding_window_detection(self, image, window_size=(512, 512), overlap=0.3):
        """
        滑动窗口检测 - 对图像进行分块检测以提高精度
        """
        print(f"🔍 开始滑动窗口检测，窗口大小: {window_size}, 重叠率: {overlap}")

        h, w = image.shape[:2]
        window_h, window_w = window_size

        # 计算步长
        step_h = int(window_h * (1 - overlap))
        step_w = int(window_w * (1 - overlap))

        all_detections = []
        window_count = 0

        # 滑动窗口遍历
        for y in range(0, h - window_h + 1, step_h):
            for x in range(0, w - window_w + 1, step_w):
                # 确保不超出边界
                y_end = min(y + window_h, h)
                x_end = min(x + window_w, w)

                # 提取窗口
                window = image[y:y_end, x:x_end]
                window_count += 1

                print(f"   处理窗口 {window_count}: [{x}:{x_end}, {y}:{y_end}]")

                # 对窗口进行OCR检测
                window_detections = self.detect_text_in_window(window, x, y)
                all_detections.extend(window_detections)

        print(f"🔍 滑动窗口检测完成，共处理 {window_count} 个窗口，检测到 {len(all_detections)} 个文字区域")
        return all_detections

    def detect_text_in_window(self, window, offset_x, offset_y):
        """
        在单个窗口中进行文字检测
        """
        window_detections = []

        # 对每个窗口进行多次检测以提高召回率
        for scale in [0.8, 1.0, 1.2]:  # 多尺度检测
            if scale != 1.0:
                new_h, new_w = int(window.shape[0] * scale), int(window.shape[1] * scale)
                scaled_window = cv2.resize(window, (new_w, new_h))
            else:
                scaled_window = window

            # 使用所有OCR引擎检测
            detections = self.detect_text_with_all_ocr_basic(scaled_window)

            # 调整坐标到原图坐标系
            for detection in detections:
                bbox = detection['bbox']
                if scale != 1.0:
                    # 缩放回原尺寸
                    bbox = [int(coord / scale) for coord in bbox]

                # 添加窗口偏移
                bbox[0] += offset_x  # x1
                bbox[1] += offset_y  # y1
                bbox[2] += offset_x  # x2
                bbox[3] += offset_y  # y2

                detection['bbox'] = bbox
                window_detections.append(detection)

        return window_detections

    def detect_text_with_all_ocr_basic(self, image):
        """
        基础的多引擎OCR检测（不使用滑动窗口）
        """
        all_text_regions = []

        # 使用CnOCR进行检测
        cnocr_regions = self.detect_text_with_cnocr(image)
        all_text_regions.extend(cnocr_regions)

        # 使用EasyOCR进行检测
        easyocr_regions = self.detect_text_with_easyocr(image)
        all_text_regions.extend(easyocr_regions)

        # 使用Tesseract进行检测
        tesseract_regions = self.detect_text_with_tesseract(image)
        all_text_regions.extend(tesseract_regions)

        return all_text_regions

    def detect_text_with_all_ocr(self, image):
        """
        使用所有OCR引擎进行文字检测和识别（包含滑动窗口）
        """
        print("🔍 开始多引擎OCR检测...")

        # 1. 全图检测
        full_image_regions = self.detect_text_with_all_ocr_basic(image)
        print(f"📝 全图检测到 {len(full_image_regions)} 个文字区域")

        # 2. 滑动窗口检测
        sliding_window_regions = self.sliding_window_detection(image)
        print(f"📝 滑动窗口检测到 {len(sliding_window_regions)} 个文字区域")

        # 3. 合并所有检测结果
        all_text_regions = full_image_regions + sliding_window_regions

        # 4. 合并和去重
        final_regions = self.ensemble_ocr_results(all_text_regions)
        print(f"✅ OCR检测到 {len(final_regions)} 个文字区域")

        return final_regions

    def detect_text_with_cnocr(self, image):
        """使用CnOCR进行全图文字检测和识别"""
        if 'cnocr' not in self.ocr_engines:
            return []
        try:
            from PIL import Image
            if image is None:
                return []
            enhanced_image = self.enhance_image_for_detection(image)
            if hasattr(enhanced_image, 'size') and enhanced_image.size == 0:
                return []
            elif hasattr(enhanced_image, 'shape') and (len(enhanced_image.shape) == 0 or any(dim == 0 for dim in enhanced_image.shape)):
                return []
            if len(enhanced_image.shape) == 3:
                image_pil = Image.fromarray(cv2.cvtColor(enhanced_image, cv2.COLOR_BGR2RGB))
            else:
                image_pil = Image.fromarray(enhanced_image)
            results = self.ocr_engines['cnocr'].ocr(image_pil)

            # 调试信息：打印原始结果
            print(f"🔍 CnOCR原始结果数量: {len(results) if results else 0}")

            text_regions = []
            if results:
                for i, result in enumerate(results):
                    text = result.get('text', '')
                    confidence = result.get('score', 0.0)
                    position = result.get('position', [])

                    print(f"   结果 {i+1}: 文字='{text}', 置信度={confidence:.3f}, 位置={position}")

                    if confidence > self.ocr_confidence_threshold and text.strip():
                        # 从position计算bbox
                        if position is not None and hasattr(position, '__len__') and len(position) >= 4:
                            # position格式: [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
                            x_coords = [point[0] for point in position]
                            y_coords = [point[1] for point in position]
                            x1, x2 = min(x_coords), max(x_coords)
                            y1, y2 = min(y_coords), max(y_coords)
                            bbox = [int(x1), int(y1), int(x2), int(y2)]

                            text_regions.append({
                                'bbox': bbox,
                                'confidence': confidence,
                                'type': 'cnocr_detection',
                                'text': text,
                                'engine': 'cnocr'
                            })
                            print(f"   ✅ 添加文字区域: '{text}' at {bbox}")
                        else:
                            # 如果没有位置信息，跳过
                            print(f"   ⚠️ 跳过无位置信息的结果: '{text}'")
                            continue
                    else:
                        print(f"   ⚠️ 跳过低置信度或空文字: '{text}' (置信度={confidence:.3f}, 阈值={self.ocr_confidence_threshold})")

            return text_regions
        except Exception as e:
            print(f"CnOCR检测错误: {e}")
            return []

    def detect_text_with_easyocr(self, image):
        """
        使用EasyOCR进行全图文字检测和识别
        """
        if 'easyocr' not in self.ocr_engines:
            return []

        try:
            if image is None:
                return []

            # 检查图像是否为空（对numpy数组使用正确的方法）
            if hasattr(image, 'size') and image.size == 0:
                return []
            elif hasattr(image, 'shape') and (len(image.shape) == 0 or any(dim == 0 for dim in image.shape)):
                return []

            # 增强图像以提高OCR精度
            enhanced_image = self.enhance_image_for_detection(image)

            # EasyOCR可以直接处理numpy数组，优化参数以提高检测精度
            results = self.ocr_engines['easyocr'].readtext(
                enhanced_image,
                width_ths=0.7,      # 降低宽度阈值以检测更多文字
                height_ths=0.7,     # 降低高度阈值以检测更多文字
                text_threshold=0.7, # 降低文字检测阈值
                low_text=0.4,       # 降低低置信度文字阈值
                link_threshold=0.4, # 降低链接阈值
                canvas_size=2560,   # 增加画布大小以提高精度
                mag_ratio=1.5       # 增加放大比例
            )

            print(f"🔍 EasyOCR原始结果数量: {len(results) if results else 0}")

            text_regions = []
            for i, (bbox, text, confidence) in enumerate(results):
                print(f"   结果 {i+1}: 文字='{text}', 置信度={confidence:.3f}, 位置={bbox}")

                if confidence > self.ocr_confidence_threshold and text.strip():
                    # EasyOCR返回的bbox格式: [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
                    x_coords = [point[0] for point in bbox]
                    y_coords = [point[1] for point in bbox]
                    x1, x2 = min(x_coords), max(x_coords)
                    y1, y2 = min(y_coords), max(y_coords)
                    bbox_rect = [int(x1), int(y1), int(x2), int(y2)]

                    text_regions.append({
                        'bbox': bbox_rect,
                        'confidence': confidence,
                        'type': 'easyocr_detection',
                        'text': text,
                        'engine': 'easyocr'
                    })
                    print(f"   ✅ 添加文字区域: '{text}' at {bbox_rect}")
                else:
                    print(f"   ⚠️ 跳过低置信度或空文字: '{text}' (置信度={confidence:.3f}, 阈值={self.ocr_confidence_threshold})")

            return text_regions
        except Exception as e:
            print(f"EasyOCR检测错误: {e}")
            return []

    def detect_text_with_tesseract(self, image):
        """
        使用Tesseract进行全图文字检测和识别
        """
        if 'tesseract' not in self.ocr_engines:
            return []

        try:
            if image is None:
                return []

            # 检查图像是否为空（对numpy数组使用正确的方法）
            if hasattr(image, 'size') and image.size == 0:
                return []
            elif hasattr(image, 'shape') and (len(image.shape) == 0 or any(dim == 0 for dim in image.shape)):
                return []

            # 增强图像以提高OCR精度
            enhanced_image = self.enhance_image_for_detection(image)

            # 配置Tesseract参数 - 支持中英文，优化检测精度
            config = '--oem 3 --psm 6 -l chi_sim+eng -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz一二三四五六七八九十接线分段引柜变压器开关电容器母线设备kVAMVa'

            # 获取详细信息包括置信度
            data = pytesseract.image_to_data(enhanced_image, config=config, output_type=pytesseract.Output.DICT)

            print(f"🔍 Tesseract原始结果数量: {len(data['text']) if data else 0}")

            text_regions = []
            n_boxes = len(data['text'])

            for i in range(n_boxes):
                text_content = data['text'][i].strip()
                confidence = float(data['conf'][i]) / 100.0  # 转换为0-1范围

                if confidence > self.ocr_confidence_threshold and text_content:
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                    bbox_rect = [int(x), int(y), int(x+w), int(y+h)]

                    text_regions.append({
                        'bbox': bbox_rect,
                        'confidence': confidence,
                        'type': 'tesseract_detection',
                        'text': text_content,
                        'engine': 'tesseract'
                    })
                    print(f"   ✅ 添加文字区域: '{text_content}' at {bbox_rect}")

            return text_regions
        except Exception as e:
            print(f"Tesseract检测错误: {e}")
            return []

    def recognize_text_cnocr(self, image_region):
        """
        使用CnOCR识别文字
        """
        if 'cnocr' not in self.ocr_engines:
            return []

        try:
            from PIL import Image
            if image_region is None:
                return []
            if hasattr(image_region, 'size') and image_region.size == 0:
                return []
            elif hasattr(image_region, 'shape') and (len(image_region.shape) == 0 or any(dim == 0 for dim in image_region.shape)):
                return []
            if len(image_region.shape) == 3:
                image_pil = Image.fromarray(cv2.cvtColor(image_region, cv2.COLOR_BGR2RGB))
            else:
                image_pil = Image.fromarray(image_region)
            results = self.ocr_engines['cnocr'].ocr(image_pil)

            text_results = []
            for result in results:
                text = result.get('text', '')
                confidence = result.get('score', 0.0)

                if confidence > self.ocr_confidence_threshold and text.strip():
                    text_results.append({
                        'text': text,
                        'confidence': confidence,
                        'bbox': result.get('position', []),
                        'engine': 'cnocr'
                    })

            return text_results
        except Exception as e:
            print(f"CnOCR识别错误: {e}")
            return []

    def recognize_text_tesseract(self, image_region):
        """使用Tesseract识别文字"""
        if 'tesseract' not in self.ocr_engines:
            return []
        try:
            if image_region is None:
                return []

            # 检查图像是否为空（对numpy数组使用正确的方法）
            if hasattr(image_region, 'size') and image_region.size == 0:
                return []
            elif hasattr(image_region, 'shape') and (len(image_region.shape) == 0 or any(dim == 0 for dim in image_region.shape)):
                return []

            # 配置Tesseract参数 - 支持中英文
            config = '--oem 3 --psm 6 -l chi_sim+eng'

            # 获取详细信息包括置信度
            data = pytesseract.image_to_data(image_region, config=config, output_type=pytesseract.Output.DICT)

            text_results = []
            n_boxes = len(data['text'])

            for i in range(n_boxes):
                text_content = data['text'][i].strip()
                confidence = float(data['conf'][i]) / 100.0  # 转换为0-1范围

                if confidence > self.ocr_confidence_threshold and text_content:
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                    bbox = [[x, y], [x+w, y], [x+w, y+h], [x, y+h]]

                    text_results.append({
                        'text': text_content,
                        'confidence': confidence,
                        'bbox': bbox,
                        'engine': 'tesseract'
                    })

            return text_results
        except Exception as e:
            print(f"Tesseract识别错误: {e}")
            return []

  

    def ensemble_ocr_results(self, all_ocr_results):
        """
        融合多个OCR引擎的结果以提高精度
        支持任意数量的OCR引擎结果
        """
        if not all_ocr_results:
            return []

        # 按置信度排序
        all_ocr_results.sort(key=lambda x: x['confidence'], reverse=True)

        # 去重和融合
        final_results = []
        for result in all_ocr_results:
            # 简单的去重策略：如果文字内容相似度高且位置接近，选择置信度更高的
            is_duplicate = False
            for existing in final_results:
                text_sim = self.text_similarity(result['text'], existing['text'])
                bbox_sim = self.bbox_similarity(result.get('bbox', []), existing.get('bbox', []))

                # 如果文字相似且位置接近，认为是重复
                if text_sim > 0.8 or (text_sim > 0.5 and bbox_sim > 0.7):
                    is_duplicate = True
                    # 如果当前结果置信度更高，替换现有结果
                    if result['confidence'] > existing['confidence']:
                        final_results.remove(existing)
                        final_results.append(result)
                    break

            if not is_duplicate:
                final_results.append(result)

        # 按置信度重新排序
        final_results.sort(key=lambda x: x['confidence'], reverse=True)

        return final_results

    def bbox_similarity(self, bbox1, bbox2):
        """
        计算两个边界框的相似度
        """
        # 检查bbox是否有效
        if bbox1 is None or bbox2 is None:
            return 0.0
        if hasattr(bbox1, '__len__') and len(bbox1) < 4:
            return 0.0
        if hasattr(bbox2, '__len__') and len(bbox2) < 4:
            return 0.0

        try:
            # 计算交集面积
            x1_inter = max(bbox1[0], bbox2[0])
            y1_inter = max(bbox1[1], bbox2[1])
            x2_inter = min(bbox1[2], bbox2[2])
            y2_inter = min(bbox1[3], bbox2[3])

            if x2_inter <= x1_inter or y2_inter <= y1_inter:
                return 0.0

            inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)

            # 计算并集面积
            area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
            area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
            union_area = area1 + area2 - inter_area

            return inter_area / union_area if union_area > 0 else 0.0
        except:
            return 0.0

    def text_similarity(self, text1, text2):
        """
        计算两个文本的相似度
        """
        if not text1 or not text2:
            return 0.0

        # 简单的字符级相似度计算
        set1 = set(text1.lower())
        set2 = set(text2.lower())

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def predict(self, image_path, save_result=True, output_dir='results'):
        """
        综合预测：目标检测 + OCR文字识别
        使用YOLO目标检测 + CnOCR文字识别
        """
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图像: {image_path}")

            # 方法1: 使用YOLO进行目标检测
            detection_results = self.detect_objects(image_path)

            # 方法2: 使用所有OCR引擎进行全图文字检测和识别
            try:
                all_ocr_text_regions = self.detect_text_with_all_ocr(image)
                print(f"✅ 所有OCR引擎检测到 {len(all_ocr_text_regions)} 个文字区域")
            except Exception as e:
                print(f"⚠️ OCR文字检测失败: {e}")
                all_ocr_text_regions = []

            # 方法3: 从目标检测结果中提取可能的文字区域
            try:
                detection_text_regions = self.extract_text_regions(image, detection_results)
            except Exception as e:
                print(f"⚠️ 从检测结果提取文字区域失败: {e}")
                detection_text_regions = []
        except Exception as e:
            print(f"⚠️ 预测初始化阶段出错: {e}")
            raise

        # OCR文字识别 - 处理CnOCR检测到的文字和其他区域
        all_text_results = []

        # 处理所有OCR引擎直接检测到的文字（已经包含文字内容）
        for region_info in all_ocr_text_regions:
            if 'text' in region_info and region_info['text'].strip():
                # CnOCR已经包含文字内容，直接添加
                text_result = {
                    'text': region_info['text'],
                    'confidence': region_info['confidence'],
                    'engine': region_info['engine'],
                    'detection_bbox': region_info['bbox'],
                    'detection_confidence': region_info['confidence'],
                    'detection_type': region_info['type'],
                    'detection_class_id': -1
                }
                all_text_results.append(text_result)

        # 处理从目标检测结果提取的文字区域
        for region_info in detection_text_regions:
            if 'region' in region_info:
                region = region_info['region']
            else:
                # 从图像中提取区域
                bbox = region_info['bbox']
                if isinstance(bbox, (list, tuple)) and len(bbox) == 4:
                    if all(isinstance(coord, (int, float)) for coord in bbox):
                        x1, y1, x2, y2 = bbox
                    else:
                        try:
                            x_coords = [point[0] for point in bbox]
                            y_coords = [point[1] for point in bbox]
                            x1, x2 = min(x_coords), max(x_coords)
                            y1, y2 = min(y_coords), max(y_coords)
                        except (IndexError, TypeError):
                            print(f"⚠️ 无法解析bbox格式: {bbox}")
                            continue
                else:
                    print(f"⚠️ 无效的bbox格式: {bbox}")
                    continue

                h, w = image.shape[:2]
                x1 = max(0, min(w-1, int(x1)))
                y1 = max(0, min(h-1, int(y1)))
                x2 = max(x1+1, min(w, int(x2)))
                y2 = max(y1+1, min(h, int(y2)))
                region = image[y1:y2, x1:x2]

            if region is None:
                continue
            if hasattr(region, 'size') and region.size == 0:
                continue
            if hasattr(region, 'shape') and (len(region.shape) == 0 or any(dim == 0 for dim in region.shape)):
                continue

            easyocr_results = self.recognize_text_easyocr(region)
            cnocr_results = self.recognize_text_cnocr(region)
            tesseract_results = self.recognize_text_tesseract(region)
            all_ocr_results = easyocr_results + cnocr_results + tesseract_results
            ensemble_results = self.ensemble_ocr_results(all_ocr_results)

            # 添加区域信息
            for text_result in ensemble_results:
                # 确保所有数据都是JSON可序列化的
                bbox = region_info['bbox']
                if isinstance(bbox, (tuple, list)):
                    bbox = [int(x) for x in bbox]

                text_result.update({
                    'detection_bbox': bbox,
                    'detection_confidence': float(region_info['confidence']),
                    'detection_type': str(region_info.get('type', 'unknown')),
                    'detection_class_id': int(region_info.get('class_id', -1))
                })

            all_text_results.extend(ensemble_results)

        # 整合结果
        final_result = {
            'image_path': image_path,
            'detections': detection_results,
            'text_recognition': all_text_results,
            'all_ocr_text_regions': len(all_ocr_text_regions),
            'detection_text_regions': len(detection_text_regions),
            'timestamp': time.time()
        }

        # 保存结果
        if save_result:
            self.save_prediction_result(final_result, image, output_dir)

        return final_result

    def draw_chinese_text(self, img, text, position, font_size=20, color=(255, 0, 0)):
        """使用PIL在图像上绘制中文文字"""
        try:
            # 将OpenCV图像转换为PIL图像
            img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(img_pil)

            # 尝试使用系统中文字体
            try:
                # Windows系统字体
                font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", font_size)
            except:
                try:
                    font = ImageFont.truetype("C:/Windows/Fonts/msyh.ttf", font_size)
                except:
                    try:
                        font = ImageFont.truetype("arial.ttf", font_size)
                    except:
                        font = ImageFont.load_default()

            draw.text(position, text, font=font, fill=color)
            return cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        except Exception as e:
            print(f"绘制中文文字失败: {e}")
            cv2.putText(img, text, position, cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            return img

    def save_prediction_result(self, result, image, output_dir):
        """
        保存预测结果（可视化图像和JSON数据）
        """
        os.makedirs(output_dir, exist_ok=True)

        # 获取文件名
        image_name = Path(result['image_path']).stem

        # 在图像上绘制结果
        result_image = image.copy()

        # 绘制目标检测框
        if len(result['detections']) > 0:
            detection_result = result['detections'][0]
            if hasattr(detection_result, 'boxes') and detection_result.boxes is not None and len(detection_result.boxes) > 0:
                boxes = detection_result.boxes
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                    confidence = float(box.conf[0])
                    class_id = int(box.cls[0])
                    cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    cv2.putText(result_image, f'Class:{class_id} {confidence:.2f}',
                               (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # 绘制OCR结果
        for text_result in result['text_recognition']:
            bbox = text_result['detection_bbox']
            text = text_result['text']
            confidence = text_result['confidence']

            # 绘制文字区域 - 蓝色 (BGR格式: 255,0,0)
            cv2.rectangle(result_image, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (255, 0, 0), 2)

            # 使用支持中文的方式添加识别的文字 - 蓝色
            text_to_show = f'{text} ({confidence:.2f})'
            result_image = self.draw_chinese_text(result_image, text_to_show,
                                                (bbox[0], bbox[3]+20), font_size=16, color=(255, 0, 0))

        # 保存可视化结果
        cv2.imwrite(os.path.join(output_dir, f'{image_name}_result.jpg'), result_image)

        # 保存JSON结果
        detections_list = []
        if len(result['detections']) > 0:
            detection_result = result['detections'][0]
            if hasattr(detection_result, 'boxes') and detection_result.boxes is not None and len(detection_result.boxes) > 0:
                detections_list = [
                    {
                        'bbox': box.xyxy[0].cpu().numpy().tolist(),
                        'confidence': float(box.conf[0]),
                        'class_id': int(box.cls[0])
                    } for box in detection_result.boxes
                ]

        json_result = {
            'image_path': result['image_path'],
            'detections': detections_list,
            'text_recognition': result['text_recognition'],
            'timestamp': result['timestamp']
        }

        with open(os.path.join(output_dir, f'{image_name}_result.json'), 'w', encoding='utf-8') as f:
            json.dump(json_result, f, ensure_ascii=False, indent=2, default=self._json_serializer)

    def _json_serializer(self, obj):
        """
        JSON序列化辅助函数，处理numpy类型
        """
        import numpy as np
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif hasattr(obj, 'item'):  # torch tensor
            return obj.item()
        elif hasattr(obj, 'tolist'):  # torch tensor
            return obj.tolist()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")


def train_yolo_model():
    """训练YOLO目标检测模型"""
    print("🚀 开始训练YOLO模型...")
    device = '0' if torch.cuda.is_available() else 'cpu'
    print(f"📱 使用设备: {'GPU' if device == '0' else 'CPU'}")
    model = YOLO(r'yolo11s.pt')

    # 训练参数配置
    training_args = {
        'data': r'yqjdataset/data.yaml',
        'epochs': 3,
        'imgsz': 640,
        'batch': 64,
        'device': device,
        'optimizer': 'AdamW',
        'lr0': 0.001,
        'project': 'high_precision_detection',
        'name': 'yolo11s_ocr_integrated',
        'save': True,
        'val': True,
        'plots': True,
    }

    # 开始训练
    results = model.train(**training_args)

    print("✅ YOLO模型训练完成!")
    return model, results

def create_multitask_model(yolo_model_path=None):
    """创建多任务模型 (YOLO目标检测 + CnOCR文字识别)"""
    print("🚀 创建多任务模型...")
    if yolo_model_path is None:
        trained_model_path = 'high_precision_detection/yolo11s_ocr_integrated/weights/best.pt'
        if os.path.exists(trained_model_path):
            yolo_model_path = trained_model_path
            print(f"✅ 使用已训练的YOLO模型: {yolo_model_path}")
        else:
            yolo_model_path = 'yolo11s.pt'
            print(f"⚠️ 使用预训练模型: {yolo_model_path}")
    model = MultiTaskModel(yolo_model_path)
    print("✅ 多任务模型创建完成!")
    return model

def load_integrated_model(model_path: str):
    """加载整合的YOLO+OCR模型，支持多种格式"""
    print(f"📂 加载整合模型: {model_path}")
    try:
        if model_path.endswith('.json'):
            import json
            with open(model_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            if config.get('model_type') == 'MultiTaskWrapper':
                print("🔧 加载多任务包装模型配置...")
                yolo_model_path = config['yolo_model_path']
                model = MultiTaskModel(yolo_model_path)
                model.detection_conf_threshold = config.get('detection_conf_threshold', 0.15)
                model.ocr_confidence_threshold = config.get('ocr_confidence_threshold', 0.05)
                print(f"✅ 多任务包装模型加载完成")
                return model
            else:
                print(f"❌ 不支持的模型类型: {config.get('model_type')}")
                return None
        elif model_path.endswith('.pt'):
            print("⚠️ 直接.pt文件加载功能已移除，请使用JSON配置文件")
            return None

        else:
            print(f"❌ 不支持的文件格式: {model_path}")
            return None

    except Exception as e:
        print(f"❌ 加载模型失败: {e}")
        import traceback
        traceback.print_exc()
        return None




def test_integrated_model(model, test_images_dir='yqjdataset/test/images', output_dir='integrated_results'):
    """
    测试整合模型的性能
    """
    print(f"🧪 开始测试整合模型...")

    # 获取测试图像
    test_images = []
    for ext in ['*.jpg', '*.jpeg', '*.png']:
        test_images.extend(Path(test_images_dir).glob(ext))

    if not test_images:
        print(f"❌ 在 {test_images_dir} 中未找到测试图像")
        return

    print(f"📊 找到 {len(test_images)} 张测试图像")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 测试模型
    results = []
    for i, image_path in enumerate(tqdm(test_images[:10], desc="测试进度")):  # 测试前10张图像
        try:
            result = model.predict(str(image_path), save_result=True, output_dir=output_dir)
            results.append(result)

            # 打印结果摘要
            detection_count = 0
            if len(result['detections']) > 0:
                detection_result = result['detections'][0]
                if hasattr(detection_result, 'boxes') and detection_result.boxes is not None:
                    detection_count = len(detection_result.boxes)
            text_count = len(result['text_recognition'])

            print(f"图像 {i+1}: 检测到 {detection_count} 个目标, 识别到 {text_count} 段文字")

        except Exception as e:
            print(f"❌ 处理图像 {image_path} 时出错: {e}")

    print(f"✅ 测试完成! 结果保存在 {output_dir}")
    return results
def main():
    """
    主函数：训练和测试整合模型
    """
    print("🎯 YOLO+OCR整合训练系统")

    # 步骤1: 训练YOLO模型
    print("\n📍 步骤1: 训练高精度YOLO目标检测模型")
    try:
        _, _ = train_yolo_model()
        print("✅ YOLO模型训练完成")
    except Exception as e:
        print(f"⚠️ YOLO训练出错，使用预训练模型: {e}")

    # 步骤2: 创建多任务整合模型
    print("\n📍 步骤2: 创建多任务整合模型 (目标检测+OCR)")
    integrated_model = create_multitask_model()
    print(f"📁 多任务模型创建完成")

    # 步骤3: 测试整合模型
    print("\n📍 步骤3: 测试整合模型性能")
    try:
        _ = test_integrated_model(integrated_model)
        print("✅ 模型测试完成")
    except Exception as e:
        print(f"⚠️ 模型测试出错: {e}")

    print("\n🎉 训练和测试完成!")
    print("📁 结果目录: high_precision_detection/yolo11s_ocr_integrated/, integrated_results/")

    return integrated_model


def demo_multitask_prediction(image_path='DaYuanTuZ_0.png'):
    """
    演示多任务模型的预测功能
    """
    print(f"🎬 演示多任务模型预测功能")
    print(f"📸 测试图像: {image_path}")

    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return

    # 创建模型
    model = create_multitask_model()

    # 进行预测
    try:
        result = model.predict(image_path, save_result=True, output_dir='demo_results')

        # 打印结果摘要
        detection_count = 0
        if len(result['detections']) > 0:
            detection_result = result['detections'][0]
            if hasattr(detection_result, 'boxes') and detection_result.boxes is not None:
                detection_count = len(detection_result.boxes)
        text_count = len(result['text_recognition'])
        print(f"\n📊 结果: 检测{detection_count}个元器件, 识别{text_count}段文字")

        if text_count > 0:
            print(f"\n📝 识别到的文字内容:")
            for i, text_result in enumerate(result['text_recognition'][:5]):  # 显示前5个
                print(f"   {i+1}. {text_result['text']} (置信度: {text_result['confidence']:.2f}, 引擎: {text_result['engine']})")

        print(f"\n💾 结果已保存到: demo_results/")

    except Exception as e:
        import traceback
        print(f"❌ 预测过程出错: {e}")
        print(f"错误详情: {traceback.format_exc()}")


if __name__ == '__main__':
    import sys
    import argparse

    parser = argparse.ArgumentParser(description='YOLO+OCR整合模型训练和预测')
    parser.add_argument('--mode', type=str, default='train',
                       choices=['train', 'predict', 'create_multitask', 'demo'],
                       help='运行模式: train(训练), predict(预测), create_multitask(创建多任务模型), demo(演示)')
    parser.add_argument('--image', type=str, default='DaYuanTuZ_0.png',
                       help='预测时使用的图像路径')
    parser.add_argument('--yolo_model', type=str, default=None,
                       help='YOLO模型路径')
    parser.add_argument('--save_path', type=str, default='models/integrated_yolo_ocr_model.pt',
                       help='整合模型保存路径')
    parser.add_argument('--load_model', type=str, default=None,
                       help='加载已保存的整合模型路径')

    args = parser.parse_args()

    if args.mode == 'train':
        # 完整训练模式
        print("🚀 开始完整训练流程...")
        model = main()

    elif args.mode == 'create_multitask':
        # 创建多任务模型模式
        print("🔧 创建多任务YOLO+OCR模型...")
        model = create_multitask_model(yolo_model_path=args.yolo_model)
        print(f"✅ 多任务模型已创建完成")

    elif args.mode == 'predict':
        # 预测模式
        print(f"🎯 预测模式，图像: {args.image}")

        if args.load_model:
            # 使用保存的整合模型
            print(f"📂 加载整合模型: {args.load_model}")
            model = load_integrated_model(args.load_model)
        else:
            # 使用多任务模型
            print("🔧 创建多任务模型...")
            model = create_multitask_model(args.yolo_model)

        # 进行预测
        if not os.path.exists(args.image):
            print(f"❌ 图像文件不存在: {args.image}")
        else:
            try:
                result = model.predict(args.image, save_result=True, output_dir='demo_results')

                # 打印结果摘要
                detection_count = 0
                if len(result['detections']) > 0:
                    detection_result = result['detections'][0]
                    if hasattr(detection_result, 'boxes') and detection_result.boxes is not None:
                        detection_count = len(detection_result.boxes)
                text_count = len(result['text_recognition'])

                print(f"📊 结果: 检测{detection_count}个元器件, 识别{text_count}段文字")
                if text_count > 0:
                    for i, text_result in enumerate(result['text_recognition'][:3]):
                        print(f"   {text_result['text']} ({text_result['confidence']:.2f})")
                print(f"💾 结果保存到: demo_results/")

            except Exception as e:
                import traceback
                print(f"❌ 预测过程出错: {e}")
                print(f"错误详情: {traceback.format_exc()}")

    elif args.mode == 'demo':
        # 演示模式
        demo_multitask_prediction(args.image)

    else:
        print(f"❌ 未知模式: {args.mode}")
        parser.print_help()